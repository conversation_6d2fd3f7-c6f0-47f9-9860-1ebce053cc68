<div class="card">
  <div class="card-header d-flex justify-content-between align-items-center">
    <strong>Print Barcode</strong>
    <button type="button" class="close" aria-label="Close" (click)="modalRef?.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="card-body">
    <div class="modal-body row">
      <div class="col-md-3 form-group">
        <label>BarCode</label>
        <input class="form-control" [(ngModel)]="barcode" disabled>
        <label>Number of Stickers</label> <input type="number"
                                                 class="form-control"
                                                 [(ngModel)]="numberOfStickersToPrint" min="1"
                                                 (ngModelChange)="onStickerCountChange()"
                                                 placeholder="Enter number of stickers">
      </div>

      <div class="col-md-9" style="font-family: sans-serif">
        <div style="text-align: center; margin-bottom: 10px; color: #666; font-size: 12px;">
          <strong>Paper Size:</strong> {{ paperSize }} |
          <strong>Columns:</strong> {{ numberOfColumns }} |
          <strong>Rows:</strong> {{ getCalculatedRows() }} |
          <strong>Total Stickers:</strong> {{ getCalculatedRows() * numberOfColumns }} |
          <strong>Sticker Size:</strong> {{ getStickerDimensions().width }} × {{ getStickerDimensions().height }}
        </div>
        <div class="row">
          <div class="col-md-12" id="print-section" style="font-family: sans-serif">
            <!-- Generate rows dynamically based on sticker count and columns -->
            <div *ngFor="let row of getRowsArray(); let rowIndex = index" style="display: table; width: 100%;">
              <div style="display: table-row;">
                <!-- Generate columns for each row -->
                <ng-container *ngFor="let col of getColumnsArray(); let colIndex = index">
                  <div *ngIf="(rowIndex * numberOfColumns + colIndex) < numberOfStickersToPrint"
                       style="display: table-cell; border: none !important; outline: none !important; margin: 0 !important; padding: 0.3mm !important; text-align: center; vertical-align: middle; width: {{getStickerDimensions().width}}; height: {{getStickerDimensions().height}}; background: transparent; box-shadow: none !important;">

                    <!-- Table-based layout for barcode sticker -->
                    <table class="sticker-table" style="width: 100%; height: 100%; border: none; border-collapse: collapse;">
                      <tr style="border: none;">
                        <!-- Left column: Price (10% width, vertical text) -->
                        <td class="price-column" style="width: 10%; border: none; padding: 0; vertical-align: middle; text-align: center;">
                          <div class="vertical-price">Rs. {{ price | number:'1.2-2' }}</div>
                        </td>

                        <!-- Middle column: Barcode and item info (80% or 90% width) -->
                        <td class="content-column" [style.width]="useCostCodes ? '80%' : '90%'" style="border: none; padding: 0.5mm; vertical-align: middle; text-align: center;">
                          <!-- Barcode section (if enabled) -->
                          <div class="barcode-section" *ngIf="showBarcode">
                            <ngx-barcode
                                     [bc-value]="getBarcodeValue()"
                                     [bc-display-value]="false"
                                     [bc-element-type]="elementType"
                                     [bc-format]="format"
                                     [bc-line-color]="lineColor"
                                     [bc-width]="width"
                                     [bc-height]="height"
                                     [bc-font-options]="fontOptions"
                                     [bc-font]="font"
                                     [bc-text-align]="textAlign"
                                     [bc-text-position]="textPosition"
                                     [bc-text-margin]="textMargin"
                                     [bc-font-size]="fontSize"
                                     [bc-background]="background"
                                     [bc-margin]="margin"
                                     [bc-margin-top]="marginTop"
                                     [bc-margin-bottom]="marginBottom"
                                     [bc-margin-left]="marginLeft"
                                     [bc-margin-right]="marginRight">
                            </ngx-barcode>
                          </div>

                          <!-- Item info section -->
                          <div class="item-info-section">
                            <!-- Item Name (always shown) with dynamic sizing -->
                            <div class="item-name" [style.font-size]="getItemNameFontSize()">{{ itemName || 'No Name' }}</div>

                            <!-- Display Code (Barcode when cost codes not used) -->
                            <div class="item-code" *ngIf="!useCostCodes">{{ getBarcodeValue() }}</div>

                            <!-- Barcode as Text (when barcode element is hidden) -->
                            <div *ngIf="!showBarcode" class="barcode-text">{{ getBarcodeValue() }}</div>
                          </div>
                        </td>

                        <!-- Right column: Cost Code (10% width, vertical text, only if cost codes enabled) -->
                        <td *ngIf="useCostCodes" class="cost-code-column" style="width: 10%; border: none; padding: 0; vertical-align: middle; text-align: center;">
                          <div class="vertical-cost-code">{{ getDisplayCode() }}</div>
                        </td>
                      </tr>
                    </table>

                  </div>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row mt-3 align-content-end d-block mr-2 text-right">
      <button class="btn btn-primary" (click)="print()">
        Print
      </button>
    </div>
  </div>
</div>
