/* Remove all borders and outlines globally */
* {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Table-based sticker layout */
.sticker-table {
  width: 100% !important;
  height: auto !important;
  min-height: 100% !important;
  border: none !important;
  border-collapse: collapse !important;
  table-layout: fixed !important;
  background: transparent !important;
}

.sticker-table td {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Vertical price column */
.price-column {
  width: 8% !important;
  vertical-align: middle !important;
  text-align: center !important;
}

.vertical-price {
  writing-mode: vertical-lr;
  text-orientation: mixed;
  transform: rotate(180deg);
  font-size: 0.5em;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  color: #000;
  white-space: nowrap;
  line-height: 1.0;
}

/* Vertical cost code column */
.cost-code-column {
  width: 8% !important;
  vertical-align: middle !important;
  text-align: center !important;
}

.vertical-cost-code {
  writing-mode: vertical-lr;
  text-orientation: mixed;
  transform: rotate(180deg);
  font-size: 0.45em;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  color: #000;
  white-space: nowrap;
  line-height: 1.0;
}

/* Content column */
.content-column {
  vertical-align: top !important;
  text-align: center !important;
  padding: 0.3mm !important;
  height: auto !important;
}

/* Barcode section */
.barcode-section {
  width: 100%;
  text-align: center;
  margin-bottom: 0.3mm;
}

/* Item info section */
.item-info-section {
  width: 100%;
  text-align: center;
  margin-top: 0.3mm;
  height: auto;
}

.item-name {
  font-weight: bold;
  font-family: 'Courier New', monospace;
  margin-bottom: 0.2mm;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  line-height: 1.1;
  max-height: 4em;
  overflow: hidden;
  white-space: normal;
  /* font-size is set dynamically based on name length */
}

.item-code {
  font-size: 0.6em;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  margin-bottom: 0.2mm;
  line-height: 1.1;
}

.barcode-text {
  font-size: 0.45em;
  font-family: monospace;
  font-weight: bold;
  line-height: 1.0;
  margin-top: 0.2mm;
  margin-bottom: 0.1mm;
  color: #333;
}

/* Legacy price styles - removed in favor of vertical layout */

/* Print-specific styles */
@media print {
  .sticker-container {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }

  #print-section > div {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    gap: 5mm !important;
    padding: 0 !important;
    margin: 0 !important;
    justify-content: flex-start !important;
    align-items: flex-start !important;
    width: 100% !important;
    height: auto !important;
  }

  #print-section > div > div {
    flex-shrink: 0 !important;
    margin-right: 5mm !important;
  }
}

/* Responsive adjustments for different paper sizes */
@media (max-width: 30mm) {
  .item-name { line-height: 0.9; }
  .item-code { font-size: 0.4em; line-height: 0.9; }
  .vertical-price { font-size: 0.4em; line-height: 0.9; }
  .vertical-cost-code { font-size: 0.35em; line-height: 0.9; }
  .barcode-text { font-size: 0.35em; line-height: 0.9; }
  .content-column { padding: 0.2mm !important; }
}

@media (min-width: 50mm) {
  .item-name { line-height: 1.1; }
  .item-code { font-size: 0.6em; line-height: 1.1; }
  .vertical-price { font-size: 0.6em; line-height: 1.1; }
  .vertical-cost-code { font-size: 0.55em; line-height: 1.1; }
  .barcode-text { font-size: 0.5em; line-height: 1.1; }
  .content-column { padding: 0.4mm !important; }
}
