/* Remove all borders and outlines globally */
* {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Table-based sticker layout */
.sticker-table {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  border-collapse: collapse !important;
  table-layout: fixed !important;
  background: transparent !important;
}

.sticker-table td {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Vertical price column */
.price-column {
  width: 10% !important;
  vertical-align: middle !important;
  text-align: center !important;
}

.vertical-price {
  writing-mode: vertical-lr;
  text-orientation: mixed;
  transform: rotate(180deg);
  font-size: 0.7em;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  color: #000;
  white-space: nowrap;
  line-height: 1.0;
}

/* Vertical cost code column */
.cost-code-column {
  width: 10% !important;
  vertical-align: middle !important;
  text-align: center !important;
}

.vertical-cost-code {
  writing-mode: vertical-lr;
  text-orientation: mixed;
  transform: rotate(180deg);
  font-size: 0.6em;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  color: #000;
  white-space: nowrap;
  line-height: 1.0;
}

/* Content column */
.content-column {
  vertical-align: middle !important;
  text-align: center !important;
  padding: 0.5mm !important;
}

/* Barcode section */
.barcode-section {
  width: 100%;
  text-align: center;
  margin-bottom: 0.5mm;
}

/* Item info section */
.item-info-section {
  width: 100%;
  text-align: center;
  margin-top: 0.5mm;
}

.item-name {
  font-weight: bold;
  font-family: 'Courier New', monospace;
  margin-bottom: 0.2mm;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.1;
  /* font-size is set dynamically based on name length */
}

.item-code {
  font-size: 0.6em;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  margin-bottom: 0.2mm;
  line-height: 1.1;
}

.barcode-text {
  font-size: 0.5em;
  font-family: monospace;
  font-weight: bold;
  line-height: 1.1;
  margin-bottom: 0.2mm;
}

/* Legacy price styles - removed in favor of vertical layout */

/* Print-specific styles */
@media print {
  .sticker-container {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }

  #print-section > div {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    gap: 5mm !important;
    padding: 0 !important;
    margin: 0 !important;
    justify-content: flex-start !important;
    align-items: flex-start !important;
    width: 100% !important;
    height: auto !important;
  }

  #print-section > div > div {
    flex-shrink: 0 !important;
    margin-right: 5mm !important;
  }
}

/* Responsive adjustments for different paper sizes */
@media (max-width: 30mm) {
  .item-name { line-height: 1.0; }
  .item-code { font-size: 0.5em; line-height: 1.0; }
  .vertical-price { font-size: 0.5em; line-height: 1.0; }
  .vertical-cost-code { font-size: 0.4em; line-height: 1.0; }
  .barcode-text { font-size: 0.4em; line-height: 1.0; }
  .content-column { padding: 0.3mm !important; }
}

@media (min-width: 50mm) {
  .item-name { line-height: 1.2; }
  .item-code { font-size: 0.7em; line-height: 1.2; }
  .vertical-price { font-size: 0.8em; line-height: 1.2; }
  .vertical-cost-code { font-size: 0.7em; line-height: 1.2; }
  .barcode-text { font-size: 0.6em; line-height: 1.2; }
  .content-column { padding: 0.8mm !important; }
}
