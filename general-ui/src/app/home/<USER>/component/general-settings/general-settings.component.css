.card-header {
  font-weight: bold;
}

.form-group label {
  font-weight: 500;
}

.alert-info {
  background-color: #e8f4f8;
  border-color: #bee5eb;
}

/* Language switcher styles */
.language-switcher {
  display: flex;
  align-items: center;
}

.language-switcher button {
  margin: 0 5px 0 0;
  min-width: 60px;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
}

/* Tab styling */
.nav-tabs {
  border-bottom: 2px solid #dee2e6;
  margin-bottom: 20px;
}

.nav-tabs .nav-link {
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
  color: #495057;
  font-weight: 500;
  padding: 12px 20px;
  transition: all 0.15s ease-in-out;
}

.nav-tabs .nav-link:hover {
  border-color: #e9ecef #e9ecef #dee2e6;
  color: #007bff;
}

.nav-tabs .nav-link.active {
  color: #007bff;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
  border-bottom: 2px solid #fff;
  margin-bottom: -2px;
}

.nav-tabs .nav-link i {
  margin-right: 8px;
}

.tab-content {
  background: #fff;
  border-radius: 0 0 0.25rem 0.25rem;
}

.tab-pane {
  padding: 20px 0;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .language-switcher button {
    padding: 0.375rem 0.5rem;
    font-size: 0.9rem;
    min-width: 50px;
  }

  .nav-tabs .nav-link {
    padding: 8px 12px;
    font-size: 0.9rem;
  }
}
