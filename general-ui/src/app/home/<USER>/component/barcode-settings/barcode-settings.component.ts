import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { BarcodeSettingsService } from '../../../core/service/barcode-settings.service';
import { BarcodeSettings, BarcodeSettingsModel } from '../../../core/model/barcode-settings';

@Component({
  selector: 'app-barcode-settings',
  templateUrl: './barcode-settings.component.html',
  styleUrls: ['./barcode-settings.component.css']
})
export class BarcodeSettingsComponent implements OnInit {
  
  // Settings data
  barcodeSettings: BarcodeSettings;
  isLoading = true;
  isSaving = false;

  // Options for dropdowns
  paperSizeOptions = BarcodeSettingsModel.getPaperSizeOptions();
  fontFamilyOptions = BarcodeSettingsModel.getFontFamilyOptions();
  columnOptions = BarcodeSettingsModel.getColumnOptions();

  constructor(
    private barcodeSettingsService: BarcodeSettingsService,
    private toastr: ToastrService
  ) {
    this.barcodeSettings = BarcodeSettingsModel.getDefaultSettings();
  }

  ngOnInit(): void {
    this.loadSettings();
  }

  /**
   * Load barcode settings
   */
  loadSettings(): void {
    this.isLoading = true;
    
    // First try to load from localStorage for immediate display
    this.barcodeSettings = this.barcodeSettingsService.getBarcodeSettings();
    this.isLoading = false;

    // Then load from database in the background
    this.barcodeSettingsService.loadBarcodeSettingsFromDatabase().subscribe(
      (settings) => {
        this.barcodeSettings = settings;
        console.log('Barcode settings loaded from database:', settings);
      },
      (error) => {
        console.error('Error loading barcode settings from database:', error);
        this.toastr.warning('Using cached settings. Database connection failed.', 'Warning');
      }
    );
  }

  /**
   * Save barcode settings
   */
  saveSettings(): void {
    this.isSaving = true;

    console.log('Starting to save barcode settings:', this.barcodeSettings);

    // Save to localStorage immediately
    this.barcodeSettingsService.saveBarcodeSettings(this.barcodeSettings);
    console.log('Saved to localStorage');

    // Save to database
    console.log('Starting database save...');
    this.barcodeSettingsService.saveBarcodeSettingsToDatabase(this.barcodeSettings).subscribe(
      (success) => {
        console.log('Database save completed with success:', success);
        this.isSaving = false;
        if (success) {
          this.toastr.success('Barcode settings saved successfully', 'Success');
        } else {
          this.toastr.warning('Settings saved locally but some database operations failed', 'Warning');
        }
      },
      (error) => {
        console.error('Database save failed with error:', error);
        this.isSaving = false;
        this.toastr.error('Error saving barcode settings', 'Error');
      }
    );
  }

  /**
   * Test database connection
   */
  testDatabaseSave(): void {
    console.log('Testing single setting save...');
    console.log('API URL being used:', (window as any).API_URL || 'Not set');

    this.barcodeSettingsService.generalSettingsService.saveOrUpdateSetting(
      'testBarcodeKey',
      'testValue',
      'Barcode',
      'Test setting for barcode'
    ).subscribe(
      (success) => {
        console.log('Test save result:', success);
        if (success) {
          this.toastr.success('Test save successful', 'Test');
        } else {
          this.toastr.error('Test save failed', 'Test');
        }
      },
      (error) => {
        console.error('Test save error:', error);
        this.toastr.error('Test save error: ' + (error.message || 'Unknown error'), 'Test');
      }
    );
  }

  /**
   * Test loading settings from database
   */
  testDatabaseLoad(): void {
    console.log('Testing settings load from database...');
    this.barcodeSettingsService.generalSettingsService.getSettingsByCategory('Barcode').subscribe(
      (settings) => {
        console.log('Loaded settings from database:', settings);
        this.toastr.info(`Loaded ${settings.length} settings from database`, 'Test Load');
      },
      (error) => {
        console.error('Test load error:', error);
        this.toastr.error('Test load error: ' + (error.message || 'Unknown error'), 'Test Load');
      }
    );
  }

  /**
   * Reset to default settings
   */
  resetToDefaults(): void {
    if (confirm('Are you sure you want to reset all barcode settings to defaults? This action cannot be undone.')) {
      this.barcodeSettings = BarcodeSettingsModel.getDefaultSettings();
      this.saveSettings();
      this.toastr.info('Barcode settings reset to defaults', 'Reset');
    }
  }

  /**
   * Clear settings from localStorage
   */
  clearLocalStorage(): void {
    if (confirm('Are you sure you want to clear barcode settings from local storage? This will reload settings from the database.')) {
      this.barcodeSettingsService.clearBarcodeSettingsFromLocalStorage();
      this.loadSettings();
      this.toastr.info('Local storage cleared. Settings reloaded from database.', 'Cleared');
    }
  }

  /**
   * Preview the current font settings
   */
  getPreviewStyle(element: string): any {
    const baseStyle = {
      fontFamily: this.barcodeSettings.fontFamily,
      fontWeight: 'bold',
      color: '#333'
    };

    switch (element) {
      case 'itemName':
        return { ...baseStyle, fontSize: this.barcodeSettings.itemNameFontSize + 'px' };
      case 'price':
        return { ...baseStyle, fontSize: this.barcodeSettings.priceFontSize + 'px' };
      case 'costCode':
        return { ...baseStyle, fontSize: this.barcodeSettings.costCodeFontSize + 'px' };
      case 'barcodeText':
        return { ...baseStyle, fontSize: this.barcodeSettings.barcodeTextFontSize + 'px' };
      default:
        return baseStyle;
    }
  }

  /**
   * Get font size range validation
   */
  getFontSizeRange(): { min: number, max: number } {
    return { min: 6, max: 24 };
  }

  /**
   * Validate font size
   */
  validateFontSize(value: number, element: string): void {
    const range = this.getFontSizeRange();
    if (value < range.min || value > range.max) {
      this.toastr.warning(`Font size for ${element} should be between ${range.min} and ${range.max} pixels`, 'Validation');
    }
  }

  /**
   * Update font size with validation
   */
  updateFontSize(element: string, value: number): void {
    const range = this.getFontSizeRange();
    const validValue = Math.max(range.min, Math.min(range.max, value));
    
    switch (element) {
      case 'itemName':
        this.barcodeSettings.itemNameFontSize = validValue;
        break;
      case 'price':
        this.barcodeSettings.priceFontSize = validValue;
        break;
      case 'costCode':
        this.barcodeSettings.costCodeFontSize = validValue;
        break;
      case 'barcodeText':
        this.barcodeSettings.barcodeTextFontSize = validValue;
        break;
    }
  }
}
