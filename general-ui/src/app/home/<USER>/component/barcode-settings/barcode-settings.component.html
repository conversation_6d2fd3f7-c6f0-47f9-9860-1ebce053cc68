<div class="barcode-settings-container">
  <div class="row">
    <!-- Left Column: Settings -->
    <div class="col-md-8">
      <form #barcodeForm="ngForm">
        
        <!-- Basic Settings Section -->
        <div class="settings-section mb-4">
          <h5 class="section-title">
            <i class="fas fa-cog"></i> Basic Settings
          </h5>
          
          <div class="row">
            <div class="col-md-6">
              <!-- Paper Size -->
              <div class="form-group mb-3">
                <label for="paperSize">Paper Size</label>
                <select
                  id="paperSize"
                  name="paperSize"
                  class="form-control"
                  [(ngModel)]="barcodeSettings.paperSize">
                  <option *ngFor="let option of paperSizeOptions" [value]="option.value">
                    {{option.label}}
                  </option>
                </select>
                <small class="form-text text-muted">Select the paper size for barcode stickers</small>
              </div>

              <!-- Columns -->
              <div class="form-group mb-3">
                <label for="columns">Columns</label>
                <select
                  id="columns"
                  name="columns"
                  class="form-control"
                  [(ngModel)]="barcodeSettings.columns">
                  <option *ngFor="let option of columnOptions" [value]="option.value">
                    {{option.label}}
                  </option>
                </select>
                <small class="form-text text-muted">Number of columns for barcode layout</small>
              </div>
            </div>

            <div class="col-md-6">
              <!-- Show Barcode -->
              <div class="form-group mb-3">
                <div class="custom-control custom-switch">
                  <input type="checkbox" class="custom-control-input" id="showBarcode"
                    [(ngModel)]="barcodeSettings.showBarcode" name="showBarcode">
                  <label class="custom-control-label" for="showBarcode">
                    Show Barcode Element
                  </label>
                </div>
                <small class="form-text text-muted">Display the actual barcode image</small>
              </div>

              <!-- Use Cost Codes -->
              <div class="form-group mb-3">
                <div class="custom-control custom-switch">
                  <input type="checkbox" class="custom-control-input" id="useCostCodes"
                    [(ngModel)]="barcodeSettings.useCostCodes" name="useCostCodes">
                  <label class="custom-control-label" for="useCostCodes">
                    Use Cost Codes
                  </label>
                </div>
                <small class="form-text text-muted">Replace numeric codes with letters</small>
              </div>
            </div>
          </div>
        </div>

        <!-- Font Settings Section -->
        <div class="settings-section mb-4">
          <h5 class="section-title">
            <i class="fas fa-font"></i> Font Settings
          </h5>

          <!-- Font Family -->
          <div class="form-group mb-3">
            <label for="fontFamily">Font Family</label>
            <select
              id="fontFamily"
              name="fontFamily"
              class="form-control"
              [(ngModel)]="barcodeSettings.fontFamily">
              <option *ngFor="let option of fontFamilyOptions" [value]="option.value">
                {{option.label}}
              </option>
            </select>
            <small class="form-text text-muted">Choose a font family that's available on all computers</small>
          </div>

          <!-- Font Sizes -->
          <div class="row">
            <div class="col-md-6">
              <!-- Item Name Font Size -->
              <div class="form-group mb-3">
                <label for="itemNameFontSize">Item Name Font Size (px)</label>
                <div class="input-group">
                  <input
                    type="number"
                    id="itemNameFontSize"
                    name="itemNameFontSize"
                    class="form-control"
                    [(ngModel)]="barcodeSettings.itemNameFontSize"
                    [min]="getFontSizeRange().min"
                    [max]="getFontSizeRange().max"
                    (blur)="validateFontSize(barcodeSettings.itemNameFontSize, 'Item Name')">
                  <div class="input-group-append">
                    <span class="input-group-text">px</span>
                  </div>
                </div>
                <small class="form-text text-muted">Font size for product names ({{getFontSizeRange().min}}-{{getFontSizeRange().max}}px)</small>
              </div>

              <!-- Price Font Size -->
              <div class="form-group mb-3">
                <label for="priceFontSize">Price Font Size (px)</label>
                <div class="input-group">
                  <input
                    type="number"
                    id="priceFontSize"
                    name="priceFontSize"
                    class="form-control"
                    [(ngModel)]="barcodeSettings.priceFontSize"
                    [min]="getFontSizeRange().min"
                    [max]="getFontSizeRange().max"
                    (blur)="validateFontSize(barcodeSettings.priceFontSize, 'Price')">
                  <div class="input-group-append">
                    <span class="input-group-text">px</span>
                  </div>
                </div>
                <small class="form-text text-muted">Font size for prices ({{getFontSizeRange().min}}-{{getFontSizeRange().max}}px)</small>
              </div>
            </div>

            <div class="col-md-6">
              <!-- Cost Code Font Size -->
              <div class="form-group mb-3">
                <label for="costCodeFontSize">Cost Code Font Size (px)</label>
                <div class="input-group">
                  <input
                    type="number"
                    id="costCodeFontSize"
                    name="costCodeFontSize"
                    class="form-control"
                    [(ngModel)]="barcodeSettings.costCodeFontSize"
                    [min]="getFontSizeRange().min"
                    [max]="getFontSizeRange().max"
                    (blur)="validateFontSize(barcodeSettings.costCodeFontSize, 'Cost Code')">
                  <div class="input-group-append">
                    <span class="input-group-text">px</span>
                  </div>
                </div>
                <small class="form-text text-muted">Font size for cost codes ({{getFontSizeRange().min}}-{{getFontSizeRange().max}}px)</small>
              </div>

              <!-- Barcode Text Font Size -->
              <div class="form-group mb-3">
                <label for="barcodeTextFontSize">Barcode Text Font Size (px)</label>
                <div class="input-group">
                  <input
                    type="number"
                    id="barcodeTextFontSize"
                    name="barcodeTextFontSize"
                    class="form-control"
                    [(ngModel)]="barcodeSettings.barcodeTextFontSize"
                    [min]="getFontSizeRange().min"
                    [max]="getFontSizeRange().max"
                    (blur)="validateFontSize(barcodeSettings.barcodeTextFontSize, 'Barcode Text')">
                  <div class="input-group-append">
                    <span class="input-group-text">px</span>
                  </div>
                </div>
                <small class="form-text text-muted">Font size for barcode text below barcode ({{getFontSizeRange().min}}-{{getFontSizeRange().max}}px)</small>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="d-flex justify-content-between">
          <div>
            <button type="button" class="btn btn-secondary mr-2" (click)="resetToDefaults()" [disabled]="isSaving">
              <i class="fas fa-undo"></i> Reset to Defaults
            </button>
            <button type="button" class="btn btn-warning" (click)="clearLocalStorage()" [disabled]="isSaving">
              <i class="fas fa-trash"></i> Clear Local Storage
            </button>
          </div>
          <button type="button" class="btn btn-primary" (click)="saveSettings()" [disabled]="isSaving">
            <i class="fas fa-save"></i> 
            <span *ngIf="!isSaving">Save Settings</span>
            <span *ngIf="isSaving">Saving...</span>
          </button>
        </div>
      </form>
    </div>

    <!-- Right Column: Preview -->
    <div class="col-md-4">
      <div class="preview-section">
        <h5 class="section-title">
          <i class="fas fa-eye"></i> Font Preview
        </h5>
        
        <div class="preview-container">
          <div class="preview-item mb-3">
            <label>Item Name:</label>
            <div class="preview-text" [ngStyle]="getPreviewStyle('itemName')">
              Sample Product Name
            </div>
          </div>

          <div class="preview-item mb-3">
            <label>Price:</label>
            <div class="preview-text" [ngStyle]="getPreviewStyle('price')">
              25.50
            </div>
          </div>

          <div class="preview-item mb-3" *ngIf="barcodeSettings.useCostCodes">
            <label>Cost Code:</label>
            <div class="preview-text" [ngStyle]="getPreviewStyle('costCode')">
              ABC
            </div>
          </div>

          <div class="preview-item mb-3" *ngIf="barcodeSettings.showBarcode">
            <label>Barcode Text:</label>
            <div class="preview-text" [ngStyle]="getPreviewStyle('barcodeText')">
              1234567890
            </div>
          </div>
        </div>

        <div class="alert alert-info mt-3">
          <i class="fas fa-info-circle"></i>
          <strong>Note:</strong> These font sizes will be applied to all barcode components. 
          Test on the target computer to ensure proper visibility.
        </div>
      </div>
    </div>
  </div>
</div>
