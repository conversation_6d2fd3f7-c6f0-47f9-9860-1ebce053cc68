import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { GeneralSettingsService } from '../../admin/service/general-settings.service';
import { BarcodeSettings, CostCodeSettings, BarcodeSettingsModel } from '../model/barcode-settings';
import { Settings } from '../model/settings';
import { HttpClient } from '@angular/common/http';
import { ApiConstants } from '../../admin/admin-constants';

// Re-export interfaces for backward compatibility
export { BarcodeSettings, CostCodeSettings } from '../model/barcode-settings';

@Injectable({
  providedIn: 'root'
})
export class BarcodeSettingsService {

  private readonly BARCODE_SETTINGS_KEY = 'barcodeSettings';
  private readonly COST_CODE_SETTINGS_KEY = 'costCodeSettings';

  constructor(
    private generalSettingsService: GeneralSettingsService,
    private http: HttpClient
  ) { }

  /**
   * Get barcode settings from localStorage with defaults
   */
  getBarcodeSettings(): BarcodeSettings {
    try {
      const saved = localStorage.getItem(this.BARCODE_SETTINGS_KEY);
      if (saved) {
        return { ...this.getDefaultBarcodeSettings(), ...JSON.parse(saved) };
      }
    } catch (error) {
      console.error('Error loading barcode settings:', error);
    }
    return this.getDefaultBarcodeSettings();
  }

  /**
   * Save barcode settings to localStorage
   */
  saveBarcodeSettings(settings: BarcodeSettings): void {
    try {
      localStorage.setItem(this.BARCODE_SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Error saving barcode settings:', error);
    }
  }

  /**
   * Get cost code settings from localStorage first, then database if not found
   */
  getCostCodeSettings(): Observable<CostCodeSettings> {
    // First try localStorage
    try {
      const saved = localStorage.getItem('costCodeLetterMapping');
      if (saved) {
        const letterMapping = JSON.parse(saved);
        return of({ letterMapping });
      }
    } catch (error) {
      console.error('Error loading cost code settings from localStorage:', error);
    }

    // If not in localStorage, try database
    return new Observable(observer => {
      this.generalSettingsService.getSettingByKey('costCodeLetterMapping').subscribe({
        next: (setting) => {
          if (setting && setting.value) {
            try {
              const letterMapping = JSON.parse(setting.value);
              // Cache to localStorage
              localStorage.setItem('costCodeLetterMapping', setting.value);
              observer.next({ letterMapping });
            } catch (error) {
              console.error('Error parsing cost code letter mapping from database:', error);
              observer.next(this.getDefaultCostCodeSettings());
            }
          } else {
            observer.next(this.getDefaultCostCodeSettings());
          }
          observer.complete();
        },
        error: (error) => {
          console.error('Error loading cost code settings from database:', error);
          observer.next(this.getDefaultCostCodeSettings());
          observer.complete();
        }
      });
    });
  }

  /**
   * Get cost code settings synchronously from localStorage (for backward compatibility)
   */
  getCostCodeSettingsSync(): CostCodeSettings {
    try {
      const saved = localStorage.getItem('costCodeLetterMapping');
      if (saved) {
        const letterMapping = JSON.parse(saved);
        return { letterMapping };
      }
    } catch (error) {
      console.error('Error loading cost code settings:', error);
    }
    return this.getDefaultCostCodeSettings();
  }

  /**
   * Save cost code settings to localStorage
   */
  saveCostCodeSettings(settings: CostCodeSettings): void {
    try {
      localStorage.setItem(this.COST_CODE_SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Error saving cost code settings:', error);
    }
  }

  /**
   * Get default barcode settings
   */
  private getDefaultBarcodeSettings(): BarcodeSettings {
    return BarcodeSettingsModel.getDefaultSettings();
  }

  /**
   * Get default cost code settings
   */
  private getDefaultCostCodeSettings(): CostCodeSettings {
    return {
      letterMapping: {
        '0': 'A',
        '1': 'B',
        '2': 'C',
        '3': 'D',
        '4': 'E',
        '5': 'F',
        '6': 'G',
        '7': 'H',
        '8': 'I',
        '9': 'J'
      }
    };
  }

  /**
   * Get barcode settings from general settings (for backward compatibility)
   */
  getBarcodeSettingsFromGeneralSettings(): BarcodeSettings {
    try {
      // Try to get from the correct localStorage key used by general settings service
      const generalSettings = JSON.parse(localStorage.getItem('app_settings') || '{}');
      const defaultSettings = this.getDefaultBarcodeSettings();
      return {
        useCostCodes: generalSettings.useCostCodes === 'true',
        showBarcode: generalSettings.showBarcode === 'true',
        columns: parseInt(generalSettings.barcodePrintColumns) || defaultSettings.columns,
        paperSize: generalSettings.barcodePaperSize || defaultSettings.paperSize,
        itemNameFontSize: parseInt(generalSettings.barcodeItemNameFontSize) || defaultSettings.itemNameFontSize,
        priceFontSize: parseInt(generalSettings.barcodePriceFontSize) || defaultSettings.priceFontSize,
        costCodeFontSize: parseInt(generalSettings.barcodeCostCodeFontSize) || defaultSettings.costCodeFontSize,
        barcodeTextFontSize: parseInt(generalSettings.barcodeTextFontSize) || defaultSettings.barcodeTextFontSize,
        fontFamily: generalSettings.barcodeFontFamily || defaultSettings.fontFamily
      };
    } catch (error) {
      console.error('Error loading settings from general settings:', error);
      return this.getDefaultBarcodeSettings();
    }
  }

  /**
   * Convert number to cost code letters based on mapping
   */
  convertNumberToCostCodeLetters(number: number): string {
    const settings = this.getCostCodeSettingsSync();
    const numberStr = number.toString();
    let result = '';

    for (let digit of numberStr) {
      result += settings.letterMapping[digit] || digit;
    }

    return result;
  }

  /**
   * Convert cost code letters back to number
   */
  convertCostCodeLettersToNumber(letters: string): number {
    const settings = this.getCostCodeSettingsSync();
    const reverseMapping: { [key: string]: string } = {};

    // Create reverse mapping
    Object.keys(settings.letterMapping).forEach(key => {
      reverseMapping[settings.letterMapping[key]] = key;
    });

    let result = '';
    for (let letter of letters) {
      result += reverseMapping[letter] || letter;
    }

    return parseInt(result, 10) || 0;
  }

  /**
   * Get CSS styles for barcode printing based on current settings
   */
  getBarcodeCSS(rows: number = 3): string {
    const settings = this.getBarcodeSettingsFromGeneralSettings();
    const columns = this.getColumnsForPaperSize(settings.paperSize);
    const fontSizes = this.getResponsiveFontSizesForPrint(settings.paperSize);
    const stickerDimensions = this.getStickerDimensions(settings.paperSize);

    return `
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        border: none !important;
        outline: none !important;
        box-shadow: none !important;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
      }

      body {
        margin: 0;
        padding: 10mm;
        font-family: Arial, sans-serif;
        background: white;
      }

      .barcode-container {
        display: flex;
        flex-wrap: wrap;
        width: calc(${columns} * (${stickerDimensions.width} + 3mm) - 3mm);
        margin: 0 auto;
        gap: 3mm;
      }

      .barcode-item {
        border: none !important;
        outline: none !important;
        box-shadow: none !important;
        padding: 0;
        text-align: center;
        background-color: transparent;
        position: relative;
        width: ${stickerDimensions.width};
        min-height: ${stickerDimensions.height};
        height: auto;
        flex-shrink: 0;
        overflow: visible;
        page-break-inside: avoid;
        box-sizing: border-box;
      }

      .barcode-code {
        font-size: ${fontSizes.code}pt;
        font-weight: bold;
        font-family: 'Courier New', 'Lucida Console', 'Monaco', 'Consolas', 'Liberation Mono', 'DejaVu Sans Mono', monospace;
        margin-bottom: 1px;
        color: #333;
        line-height: 1.1;
      }

      .barcode-name {
        margin-bottom: 1px;
        color: #333;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
        line-height: 1.1;
        max-height: 4em;
        overflow: hidden;
        white-space: normal;
        font-weight: bold;
        font-family: 'Courier New', 'Lucida Console', 'Monaco', 'Consolas', 'Liberation Mono', 'DejaVu Sans Mono', monospace;
        /* font-size is set dynamically based on name length */
      }

      .sticker-table {
        width: 100% !important;
        height: auto !important;
        min-height: 100% !important;
        border: none !important;
        border-collapse: collapse !important;
        table-layout: fixed !important;
        background: transparent !important;
      }

      .sticker-table td {
        border: none !important;
        outline: none !important;
        box-shadow: none !important;
        padding: 0 !important;
        margin: 0 !important;
      }

      .barcode-price {
        writing-mode: vertical-lr;
        text-orientation: mixed;
        transform: rotate(180deg);
        font-size: ${fontSizes.price * 0.7}pt;
        font-weight: bold;
        font-family: 'Courier New', 'Lucida Console', 'Monaco', 'Consolas', 'Liberation Mono', 'DejaVu Sans Mono', monospace;
        color: #000;
        line-height: 1.0;
        white-space: nowrap;
      }

      .barcode-cost-code {
        writing-mode: vertical-lr;
        text-orientation: mixed;
        transform: rotate(180deg);
        font-size: ${fontSizes.code * 0.65}pt;
        font-weight: bold;
        font-family: 'Courier New', 'Lucida Console', 'Monaco', 'Consolas', 'Liberation Mono', 'DejaVu Sans Mono', monospace;
        color: #000;
        line-height: 1.0;
        white-space: nowrap;
      }

      .barcode-text {
        font-size: ${fontSizes.barcode}pt;
        margin-top: 1px;
        margin-bottom: 1px;
        color: #333;
        font-family: 'Courier New', 'Lucida Console', 'Monaco', 'Consolas', 'Liberation Mono', 'DejaVu Sans Mono', monospace;
        font-weight: bold;
        line-height: 1.0;
      }

      .barcode-svg-container {
        width: 100%;
        text-align: center;
        margin-bottom: 1px;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 28px;
        overflow: hidden;
      }

      .barcode-svg {
        max-width: 100%;
        height: 25px;
        display: block;
      }

      @media print {
        body {
          margin: 0;
          padding: 5mm;
        }

        .barcode-container {
          gap: 2mm;
        }

        .item-info-section {
          height: auto !important;
        }

        .barcode-item {
          border: none !important;
          background-color: white !important;
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
          height: auto !important;
          min-height: auto !important;
          overflow: visible !important;
        }

        .barcode-name {
          white-space: normal !important;
          word-wrap: break-word !important;
          overflow-wrap: break-word !important;
          hyphens: auto !important;
          max-height: none !important;
          overflow: visible !important;
          height: auto !important;
          line-height: 1.1 !important;
        }

        .sticker-table {
          height: auto !important;
          min-height: auto !important;
        }

        .content-column {
          height: auto !important;
          vertical-align: top !important;
        }
      }

      @page {
        size: auto;
        margin: 0;
      }

      /* Ensure text doesn't get cut off in print */
      @media print {
        * {
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }

        .barcode-name {
          page-break-inside: avoid !important;
          break-inside: avoid !important;
        }
      }
    `;
  }

  /**
   * Get responsive font sizes for print based on paper size
   */
  private getResponsiveFontSizesForPrint(paperSize: string): { code: number, name: number, price: number, barcode: number } {
    const fontSizes = {
      '30x20': { code: 4, name: 3.5, price: 3.5, barcode: 3 },      // Very small stickers
      '33x21': { code: 5, name: 4, price: 4, barcode: 3.5 },        // Small stickers
      '38x25': { code: 6, name: 4.5, price: 5, barcode: 4 },        // Medium stickers
      '50x25': { code: 7, name: 5.5, price: 6, barcode: 5 },        // Medium-large stickers
      '65x15': { code: 4, name: 2.5, price: 3, barcode: 2.5 },      // Very narrow stickers
      '100x50': { code: 9, name: 7, price: 8, barcode: 7 },         // Large stickers
      '100x150': { code: 12, name: 9, price: 10, barcode: 9 }       // Extra large stickers
    };
    return fontSizes[paperSize] || fontSizes['30x20'];
  }

  /**
   * Get dynamic font size for item name based on its length and paper size
   */
  private getDynamicNameFontSize(itemName: string, paperSize: string): number {
    const nameLength = (itemName || '').length;

    // Base font sizes for different paper sizes and name lengths (more compact)
    const baseSizes = {
      '30x20': { short: 4, medium: 3.5, long: 2.5 },
      '33x21': { short: 5, medium: 4, long: 3.5 },
      '38x25': { short: 6, medium: 5, long: 4 },
      '50x25': { short: 7, medium: 6, long: 5 },
      '65x15': { short: 3.5, medium: 2.5, long: 2 }, // Very narrow stickers
      '100x50': { short: 9, medium: 8, long: 7 },
      '100x150': { short: 11, medium: 10, long: 9 }
    };

    const sizes = baseSizes[paperSize] || baseSizes['30x20'];

    // Determine size based on name length
    if (nameLength <= 10) {
      return sizes.short;  // Short names get larger text
    } else if (nameLength <= 20) {
      return sizes.medium; // Medium names get medium text
    } else {
      return sizes.long;   // Long names get smaller text
    }
  }

  /**
   * Get paper size CSS value
   */
  private getPaperSize(paperSize: string): string {
    const sizes = {
      '30x20': '30mm 20mm',
      '33x21': '33mm 21mm',
      '38x25': '38mm 25mm',
      '50x25': '50mm 25mm',
      '65x15': '65mm 15mm',
      '100x50': '100mm 50mm',
      '100x150': '100mm 150mm'
    };
    return sizes[paperSize] || '30mm 20mm';
  }

  /**
   * Get columns based on paper size
   */
  private getColumnsForPaperSize(paperSize: string): number {
    const columns = {
      '30x20': 3,
      '33x21': 3,
      '38x25': 2,
      '50x25': 2,
      '65x15': 1,
      '100x50': 1,
      '100x150': 1
    };
    return columns[paperSize] || 3;
  }

  /**
   * Get sticker dimensions based on paper size
   */
  private getStickerDimensions(paperSize: string): { width: string, height: string } {
    const dimensions = {
      '30x20': { width: '30mm', height: '20mm' },
      '33x21': { width: '33mm', height: '21mm' },
      '38x25': { width: '38mm', height: '25mm' },
      '50x25': { width: '50mm', height: '25mm' },
      '65x15': { width: '65mm', height: '15mm' },
      '100x50': { width: '100mm', height: '50mm' },
      '100x150': { width: '100mm', height: '150mm' }
    };
    return dimensions[paperSize] || dimensions['30x20'];
  }

  /**
   * Generate barcode HTML for printing
   */
  generateBarcodeHTML(items: any[], rows: number = 3): string {
    const settings = this.getBarcodeSettingsFromGeneralSettings();
    const costCodeSettings = this.getCostCodeSettingsSync();
    const columns = this.getColumnsForPaperSize(settings.paperSize);

    // Calculate maximum items to display based on rows and columns
    const maxItems = rows * columns;
    const itemsToDisplay = items.slice(0, maxItems);

    let itemsHTML = '';
    itemsToDisplay.forEach(item => {
      let code = '';
      if (settings.useCostCodes) {
        // Use cost code with letters if available, otherwise use original code
        if (item.itemCost && item.itemCost > 0) {
          // Use item cost to generate cost code
          const costNumber = Math.round(item.itemCost * 100);
          code = this.convertNumberToCostCodeLetters(costNumber);
        } else if (item.costCode) {
          // Fallback to existing cost code logic
          const numericPart = item.costCode.replace(/[^0-9]/g, '');
          if (numericPart) {
            const number = parseInt(numericPart, 10);
            if (!isNaN(number)) {
              const letters = this.convertNumberToCostCodeLetters(number);
              const prefix = item.costCode.replace(numericPart, '');
              code = `${prefix}${letters}`;
            } else {
              code = item.costCode;
            }
          } else {
            code = item.costCode;
          }
        } else {
          code = item.itemCode || 'N/A';
        }
      } else {
        code = item.barcode || item.itemCode;
      }

      // Generate barcode SVG or text based on settings
      let barcodeHTML = '';
      if (settings.showBarcode) {
        // Generate SVG barcode using JsBarcode
        const barcodeValue = item.barcode || item.itemCode || 'N/A';
        const uniqueId = `barcode-${Math.random().toString(36).substr(2, 9)}`;
        barcodeHTML = `
          <div class="barcode-svg-container">
            <svg class="barcode-svg" id="${uniqueId}" data-value="${barcodeValue}"></svg>
          </div>
        `;
      } else {
        // Show barcode text when barcode element is disabled
        barcodeHTML = `
          <div class="barcode-text">${item.barcode || item.itemCode || 'N/A'}</div>
        `;
      }

      // Get dynamic font size for this item's name from settings
      const baseFontSize = settings.itemNameFontSize || 12;
      const nameLength = (item.itemName || '').length;
      let dynamicNameFontSize = baseFontSize;
      if (nameLength > 20) {
        dynamicNameFontSize = baseFontSize * 0.8;
      } else if (nameLength > 10) {
        dynamicNameFontSize = baseFontSize * 0.9;
      }

      // Determine if cost codes are enabled
      const useCostCodes = settings.useCostCodes;
      const contentWidth = useCostCodes ? '84%' : '92%';

      itemsHTML += `
        <div class="barcode-item">
          <table class="sticker-table" style="width: 100%; height: auto; border: none; border-collapse: collapse; table-layout: fixed;">
            <tr style="border: none;">
              <td class="price-column" style="width: 8%; border: none; padding: 0; vertical-align: middle; text-align: center;">
                <div class="barcode-price" style="font-size: ${settings.priceFontSize || 10}px; font-family: ${settings.fontFamily || 'Arial, sans-serif'};">${(item.sellingPrice || 0).toFixed(2)}</div>
              </td>
              <td class="content-column" style="width: ${contentWidth}; border: none; padding: 0.3mm; vertical-align: top; text-align: center; height: auto;">
                ${barcodeHTML}
                ${settings.showBarcode ? `<div class="barcode-text" style="font-size: ${settings.barcodeTextFontSize || 8}px; font-family: ${settings.fontFamily || 'Arial, sans-serif'}; margin-top: 1px;">${item.barcode || item.itemCode || 'N/A'}</div>` : ''}
                <div class="barcode-name" style="font-size: ${dynamicNameFontSize}px; font-family: ${settings.fontFamily || 'Arial, sans-serif'}; word-wrap: break-word; overflow-wrap: break-word; white-space: normal; line-height: 1.1; height: auto; overflow: visible; hyphens: auto;">${item.itemName || 'Unknown Item'}</div>
                ${!useCostCodes && !settings.showBarcode ? `<div class="barcode-code" style="font-family: ${settings.fontFamily || 'Arial, sans-serif'};">${item.barcode || item.itemCode || 'N/A'}</div>` : ''}
              </td>
              ${useCostCodes ? `
              <td class="cost-code-column" style="width: 8%; border: none; padding: 0; vertical-align: middle; text-align: center;">
                <div class="barcode-cost-code" style="font-size: ${settings.costCodeFontSize || 9}px; font-family: ${settings.fontFamily || 'Arial, sans-serif'};">${code}</div>
              </td>` : ''}
            </tr>
          </table>
        </div>
      `;
    });

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Barcode Print</title>
        <style>
          ${this.getBarcodeCSS(rows)}
        </style>
        <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
      </head>
      <body>
        <div class="barcode-container">
          ${itemsHTML}
        </div>
        <script>
          // Initialize all barcodes after page load
          window.addEventListener('load', function() {
            setTimeout(function() {
              const svgElements = document.querySelectorAll('.barcode-svg');
              svgElements.forEach((svg) => {
                const barcodeValue = svg.getAttribute('data-value');
                if (svg && barcodeValue && typeof JsBarcode !== 'undefined') {
                  try {
                    JsBarcode(svg, barcodeValue, {
                      format: "CODE128",
                      width: 1.2,
                      height: 25,
                      displayValue: false,
                      background: "#ffffff",
                      lineColor: "#000000",
                      margin: 0
                    });
                  } catch (e) {
                    console.error('Barcode generation error:', e);
                    // Fallback: show the barcode value as text
                    svg.outerHTML = '<div class="barcode-text">' + barcodeValue + '</div>';
                  }
                }
              });
            }, 100);
          });
        </script>
      </body>
      </html>
    `;
  }

  /**
   * Save barcode settings to database
   */
  saveBarcodeSettingsToDatabase(settings: BarcodeSettings): Observable<boolean> {
    const settingsToSave = [
      { key: 'useCostCodes', value: settings.useCostCodes.toString(), category: 'Barcode', description: 'Use cost codes in barcodes' },
      { key: 'showBarcode', value: settings.showBarcode.toString(), category: 'Barcode', description: 'Show barcode element' },
      { key: 'barcodePrintColumns', value: settings.columns.toString(), category: 'Barcode', description: 'Number of columns for barcode printing' },
      { key: 'barcodePaperSize', value: settings.paperSize, category: 'Barcode', description: 'Paper size for barcode printing' },
      { key: 'barcodeItemNameFontSize', value: settings.itemNameFontSize.toString(), category: 'Barcode', description: 'Font size for item names in barcodes' },
      { key: 'barcodePriceFontSize', value: settings.priceFontSize.toString(), category: 'Barcode', description: 'Font size for prices in barcodes' },
      { key: 'barcodeCostCodeFontSize', value: settings.costCodeFontSize.toString(), category: 'Barcode', description: 'Font size for cost codes in barcodes' },
      { key: 'barcodeTextFontSize', value: settings.barcodeTextFontSize.toString(), category: 'Barcode', description: 'Font size for barcode text' },
      { key: 'barcodeFontFamily', value: settings.fontFamily, category: 'Barcode', description: 'Font family for barcode text' }
    ];

    let savedCount = 0;
    let errorCount = 0;
    const totalSettings = settingsToSave.length;

    return new Observable(observer => {
      settingsToSave.forEach(setting => {
        this.generalSettingsService.saveOrUpdateSetting(setting.key, setting.value, setting.category, setting.description).subscribe(
          (success) => {
            savedCount++;
            if (savedCount + errorCount === totalSettings) {
              // Save to localStorage as well
              this.saveBarcodeSettings(settings);
              observer.next(errorCount === 0);
              observer.complete();
            }
          },
          (error) => {
            console.error(`Error saving barcode setting ${setting.key}:`, error);
            errorCount++;
            if (savedCount + errorCount === totalSettings) {
              // Save to localStorage even if database save fails
              this.saveBarcodeSettings(settings);
              observer.next(false);
              observer.complete();
            }
          }
        );
      });
    });
  }

  /**
   * Load barcode settings from database
   */
  loadBarcodeSettingsFromDatabase(): Observable<BarcodeSettings> {
    return new Observable(observer => {
      this.generalSettingsService.getSettingsByCategory('Barcode').subscribe(
        (settings) => {
          const barcodeSettings = this.getDefaultBarcodeSettings();

          if (Array.isArray(settings)) {
            settings.forEach(setting => {
              switch (setting.key) {
                case 'useCostCodes':
                  barcodeSettings.useCostCodes = setting.value === 'true';
                  break;
                case 'showBarcode':
                  barcodeSettings.showBarcode = setting.value === 'true';
                  break;
                case 'barcodePrintColumns':
                  barcodeSettings.columns = parseInt(setting.value) || barcodeSettings.columns;
                  break;
                case 'barcodePaperSize':
                  barcodeSettings.paperSize = setting.value || barcodeSettings.paperSize;
                  break;
                case 'barcodeItemNameFontSize':
                  barcodeSettings.itemNameFontSize = parseInt(setting.value) || barcodeSettings.itemNameFontSize;
                  break;
                case 'barcodePriceFontSize':
                  barcodeSettings.priceFontSize = parseInt(setting.value) || barcodeSettings.priceFontSize;
                  break;
                case 'barcodeCostCodeFontSize':
                  barcodeSettings.costCodeFontSize = parseInt(setting.value) || barcodeSettings.costCodeFontSize;
                  break;
                case 'barcodeTextFontSize':
                  barcodeSettings.barcodeTextFontSize = parseInt(setting.value) || barcodeSettings.barcodeTextFontSize;
                  break;
                case 'barcodeFontFamily':
                  barcodeSettings.fontFamily = setting.value || barcodeSettings.fontFamily;
                  break;
              }
            });
          }

          // Save to localStorage
          this.saveBarcodeSettings(barcodeSettings);
          observer.next(barcodeSettings);
          observer.complete();
        },
        (error) => {
          console.error('Error loading barcode settings from database:', error);
          // Return default settings if database load fails
          const defaultSettings = this.getDefaultBarcodeSettings();
          observer.next(defaultSettings);
          observer.complete();
        }
      );
    });
  }

  /**
   * Clear barcode settings from localStorage
   */
  clearBarcodeSettingsFromLocalStorage(): void {
    try {
      localStorage.removeItem(this.BARCODE_SETTINGS_KEY);
      localStorage.removeItem(this.COST_CODE_SETTINGS_KEY);
      localStorage.removeItem('costCodeLetterMapping');
      console.log('Barcode settings cleared from localStorage');
    } catch (error) {
      console.error('Error clearing barcode settings from localStorage:', error);
    }
  }
}
